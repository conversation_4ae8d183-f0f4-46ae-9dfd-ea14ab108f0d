#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to recreate database tables with updated schema.
This will drop all existing data and recreate tables.
"""

import os
from sqlalchemy.orm import Session
from database.database import SessionLocal, engine
from models import Base

def recreate_database():
    """Drop and recreate all database tables."""
    print("🔄 Recreating database tables...")
    
    # Drop all tables
    Base.metadata.drop_all(bind=engine)
    print("✅ Dropped existing tables")
    
    # Create all tables with new schema
    Base.metadata.create_all(bind=engine)
    print("✅ Created tables with updated schema")
    
    print("\n📋 Database recreated successfully!")
    print("   Run create_sample_data.py to populate with sample data")

if __name__ == "__main__":
    recreate_database()
