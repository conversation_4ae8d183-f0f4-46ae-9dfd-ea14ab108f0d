# Pravasi ePaper Admin Dashboard

A comprehensive React-based admin dashboard for managing the Pravasi ePaper platform. Built with modern web technologies and featuring a clean, responsive interface.

## 🚀 Features

- **User Management**: Complete CRUD operations for user accounts
- **Subscription Management**: Handle subscription plans and user subscriptions
- **ePaper Management**: Upload, organize, and manage ePaper content
- **Analytics Dashboard**: Real-time statistics and interactive charts
- **Authentication**: Secure JWT-based authentication system
- **Responsive Design**: Mobile-friendly interface using Tailwind CSS

## 🛠️ Technology Stack

- **Frontend**: React 18+
- **UI Framework**: Custom React Components
- **Styling**: Tailwind CSS
- **Charts**: Chart.js with react-chartjs-2
- **HTTP Client**: Axios
- **Authentication**: JWT with React Context
- **Notifications**: React Toastify
- **Icons**: Font Awesome

## 📋 Prerequisites

- Node.js (v14 or higher)
- npm or yarn package manager
- FastAPI backend running (see ../api/STEPS.md)

## 🔧 Installation

1. **<PERSON><PERSON> and navigate to the dashboard directory**
   ```bash
   cd admin-dashboard
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment variables**
   Create a `.env` file:
   ```env
   REACT_APP_API_URL=http://localhost:8000
   REACT_APP_APP_NAME=Pravasi ePaper Admin
   ```

4. **Start the development server**
   ```bash
   npm start
   ```

5. **Access the dashboard**
   Open `http://localhost:3000` in your browser

## 📁 Project Structure

```
admin-dashboard/
├── public/                 # Static assets
├── src/
│   ├── components/         # Reusable UI components
│   │   ├── Cards/         # Dashboard cards
│   │   ├── Navbars/       # Navigation components
│   │   └── Sidebar/       # Sidebar navigation
│   ├── contexts/          # React contexts
│   │   └── AuthContext.js # Authentication context
│   ├── layouts/           # Page layouts
│   │   └── Admin.js       # Admin layout with routing
│   ├── services/          # API services
│   │   └── api.js         # Axios API client
│   ├── views/             # Page components
│   │   └── admin/         # Admin pages
│   │       ├── Dashboard.js    # Analytics dashboard
│   │       ├── Users.js        # User management
│   │       ├── Subscriptions.js # Subscription management
│   │       └── EPapers.js      # ePaper management
│   └── App.js             # Main application component
├── .env                   # Environment variables
├── package.json           # Dependencies and scripts
├── STEPS.md              # Detailed setup guide
└── README.md             # This file
```

## 🎯 Usage

### Authentication
- Login with admin credentials to access the dashboard
- All routes are protected and require authentication
- JWT tokens are automatically managed

### User Management
- View all registered users
- Edit user profiles and roles
- Manage user status and permissions

### Subscription Management
- Monitor all user subscriptions
- View subscription plans and pricing
- Track subscription analytics

### ePaper Management
- Upload new ePaper PDF files
- Organize content with metadata
- Preview and download functionality

### Analytics
- View real-time platform statistics
- Monitor user growth and engagement
- Track subscription revenue and trends

## 🔌 API Integration

The dashboard integrates with the FastAPI backend through RESTful APIs:

- **Authentication**: `/auth/*` endpoints
- **Users**: `/users/*` endpoints  
- **Subscriptions**: `/subscriptions/*` endpoints
- **ePapers**: `/epaper/*` endpoints

All API calls include proper authentication headers and error handling.

## 🎨 Customization

### Branding
- Update app name in `.env` file
- Modify sidebar branding in `Sidebar.js`
- Customize colors using Tailwind CSS classes

### Adding Features
1. Create new components in `src/views/admin/`
2. Add routes in `src/layouts/Admin.js`
3. Update sidebar navigation
4. Add corresponding API endpoints

## 🚀 Production Build

```bash
# Build for production
npm run build

# The build folder contains optimized static files
# Deploy to your preferred hosting platform
```

## 🔒 Security Features

- JWT-based authentication
- Protected routes and components
- Role-based access control
- Secure API communication
- Input validation and sanitization

## 🐛 Troubleshooting

### Common Issues

1. **Login Problems**
   - Verify backend is running
   - Check API URL configuration
   - Ensure admin user exists

2. **Chart Display Issues**
   - Verify chart.js installation
   - Check data fetching logic
   - Review console for errors

3. **File Upload Problems**
   - Check file size limits
   - Verify PDF format
   - Test backend upload endpoint

### Debug Tips
- Use browser developer tools
- Check network requests
- Monitor console errors
- Use React Developer Tools

## 📝 Scripts

```bash
# Start development server
npm start

# Build for production
npm run build

# Run tests
npm test

# Eject from Create React App (irreversible)
npm run eject
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is part of the Pravasi ePaper platform. All rights reserved.

## 📞 Support

For technical support:
- Review the STEPS.md file for detailed setup instructions
- Check the troubleshooting section above
- Consult the FastAPI backend documentation
- Review React and Tailwind CSS documentation

---

**Built with ❤️ for the Pravasi ePaper platform**
