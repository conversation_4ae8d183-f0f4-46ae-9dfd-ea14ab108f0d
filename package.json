{"name": "notus-react", "version": "1.1.0", "description": "Notus React - A free Tailwind CSS and React UI Kit and Admin by Creative Tim.", "repository": "https://github.com/creativetimofficial/notus-react", "license": "MIT", "dependencies": {"@fortawesome/fontawesome-free": "5.15.3", "@popperjs/core": "2.9.1", "@tailwindcss/forms": "0.2.1", "@tanstack/react-query": "^5.81.2", "autoprefixer": "10.2.5", "axios": "^1.10.0", "chart.js": "^2.9.4", "gulp": "4.0.2", "gulp-append-prepend": "1.0.8", "js-cookie": "^3.0.5", "postcss": "8.4.39", "react": "18.3.1", "react-dom": "18.3.1", "react-query": "^3.39.3", "react-router": "5.3.4", "react-router-dom": "5.3.4", "react-scripts": "5.0.1", "react-toastify": "^11.0.5", "tailwindcss": "2.0.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build && gulp licenses", "test": "react-scripts test", "eject": "react-scripts eject", "install:clean": "rm -rf node_modules/ && rm -rf package-lock.json && npm install && npm run build:tailwind && npm start", "build:tailwind": "tailwind build src/assets/styles/index.css -o src/assets/styles/tailwind.css"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "optionalDependencies": {"typescript": "4.2.3"}}