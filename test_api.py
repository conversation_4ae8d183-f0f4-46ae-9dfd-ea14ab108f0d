#!/usr/bin/env python3
"""
Simple test script to verify API endpoints are working.
Make sure the server is running before executing this script.
"""

import requests
import json
from datetime import datetime

BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test basic health check endpoint."""
    print("🔍 Testing health check...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ Health check passed")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_user_registration():
    """Test user registration."""
    print("\n🔍 Testing user registration...")
    try:
        user_data = {
            "email": "<EMAIL>",
            "username": "newuser",
            "full_name": "New Test User",
            "password": "testpass123"
        }
        response = requests.post(f"{BASE_URL}/auth/register", json=user_data)
        if response.status_code == 201:
            print("✅ User registration successful")
            return response.json()
        else:
            print(f"❌ User registration failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ User registration error: {e}")
        return None

def test_user_login(email, password):
    """Test user login."""
    print(f"\n🔍 Testing user login for {email}...")
    try:
        login_data = {
            "username": email,
            "password": password
        }
        response = requests.post(f"{BASE_URL}/auth/login", data=login_data)
        if response.status_code == 200:
            print("✅ User login successful")
            return response.json()
        else:
            print(f"❌ User login failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ User login error: {e}")
        return None

def test_protected_endpoint(token):
    """Test protected endpoint with token."""
    print("\n🔍 Testing protected endpoint...")
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{BASE_URL}/users/profile", headers=headers)
        if response.status_code == 200:
            print("✅ Protected endpoint access successful")
            return response.json()
        else:
            print(f"❌ Protected endpoint failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Protected endpoint error: {e}")
        return None

def test_subscription_plans():
    """Test subscription plans endpoint."""
    print("\n🔍 Testing subscription plans...")
    try:
        response = requests.get(f"{BASE_URL}/subscriptions/plans")
        if response.status_code == 200:
            plans = response.json()
            print(f"✅ Found {len(plans)} subscription plans")
            for plan in plans:
                print(f"   - {plan['name']}: ${plan['price']} ({plan['plan_type']})")
            return plans
        else:
            print(f"❌ Subscription plans failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Subscription plans error: {e}")
        return None

def test_subscription_status(token):
    """Test subscription status endpoint."""
    print("\n🔍 Testing subscription status...")
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{BASE_URL}/subscriptions/status", headers=headers)
        if response.status_code == 200:
            status = response.json()
            print(f"✅ Subscription status: {'Active' if status['is_active'] else 'Inactive'}")
            if status['is_active']:
                print(f"   Plan: {status['current_plan']['name']}")
                print(f"   Days remaining: {status['days_remaining']}")
            return status
        else:
            print(f"❌ Subscription status failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Subscription status error: {e}")
        return None

def test_epaper_list(token):
    """Test ePaper list endpoint."""
    print("\n🔍 Testing ePaper list...")
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{BASE_URL}/epaper/", headers=headers)
        if response.status_code == 200:
            epapers = response.json()
            print(f"✅ Found {len(epapers)} ePapers")
            return epapers
        else:
            print(f"❌ ePaper list failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ ePaper list error: {e}")
        return None

def main():
    """Run all tests."""
    print("🚀 Starting API Tests for Pravasi ePaper Platform")
    print("=" * 50)
    
    # Test health check
    if not test_health_check():
        print("❌ Server is not running or not responding")
        return
    
    # Test subscription plans (public endpoint)
    test_subscription_plans()
    
    # Test user registration
    new_user = test_user_registration()
    
    # Test login with existing user
    login_result = test_user_login("<EMAIL>", "user123")
    if login_result:
        token = login_result["access_token"]
        
        # Test protected endpoints
        test_protected_endpoint(token)
        test_subscription_status(token)
        test_epaper_list(token)
    
    # Test admin login
    admin_login = test_user_login("<EMAIL>", "admin123")
    if admin_login:
        admin_token = admin_login["access_token"]
        print(f"\n✅ Admin login successful")
        print(f"   Admin can access all endpoints with token: {admin_token[:20]}...")
    
    print("\n" + "=" * 50)
    print("🎉 API Tests Completed!")
    print("\n📋 Next Steps:")
    print("   1. Open http://localhost:8000/docs to see interactive API documentation")
    print("   2. Test file upload functionality through the docs interface")
    print("   3. Create some ePaper content for testing")

if __name__ == "__main__":
    main()
