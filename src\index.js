import React from "react";
import ReactDOM from "react-dom";
import { BrowserRouter, Route, Switch, Redirect } from "react-router-dom";
import { ToastContainer } from 'react-toastify';

import "@fortawesome/fontawesome-free/css/all.min.css";
import "assets/styles/tailwind.css";
import 'react-toastify/dist/ReactToastify.css';

// contexts
import { AuthProvider } from "contexts/AuthContext";

// layouts
import Admin from "layouts/Admin.js";
import Auth from "layouts/Auth.js";

// Main App Component with Authentication Logic
import App from "components/App.js";

ReactDOM.render(
  <AuthProvider>
    <BrowserRouter>
      <App />
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
    </BrowserRouter>
  </AuthProvider>,
  document.getElementById("root")
);
