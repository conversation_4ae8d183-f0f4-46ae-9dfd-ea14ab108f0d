# Pravasi ePaper Admin Dashboard - Setup Guide

## Overview
This is the admin dashboard for the Pravasi ePaper platform, built using React and the Notus React template. It provides a comprehensive interface for managing users, subscriptions, ePaper content, and viewing analytics.

## Prerequisites
- Node.js (v14 or higher)
- npm or yarn package manager
- FastAPI backend running (see ../api/STEPS.md)

## Installation Steps

### Step 1: Install Dependencies
```bash
cd admin-dashboard
npm install
```

### Step 2: Environment Configuration
Create a `.env` file in the root directory:
```env
REACT_APP_API_URL=http://localhost:8000
REACT_APP_APP_NAME=Pravasi ePaper Admin
```

### Step 3: Start Development Server
```bash
npm start
```
The dashboard will be available at `http://localhost:3000`

## Features

### 1. Authentication System
- JWT-based authentication with the FastAPI backend
- Secure login/logout functionality
- Protected routes for admin access only
- Automatic token refresh and session management

### 2. User Management
- View all registered users
- Edit user details and roles
- Manage user status (active/inactive)
- Role-based access control (admin/user)

### 3. Subscription Management
- View all user subscriptions with status tracking
- Manage subscription plans
- Monitor subscription analytics
- Track subscription revenue and growth

### 4. ePaper Management
- Upload new ePaper PDF files
- Organize and categorize content
- Preview and download functionality
- File management with metadata

### 5. Analytics Dashboard
- Real-time statistics and metrics
- Interactive charts and graphs
- Monthly growth tracking
- User engagement analytics
- Revenue and subscription insights

## File Structure
```
admin-dashboard/
├── public/
├── src/
│   ├── components/
│   │   ├── Sidebar/
│   │   ├── Navbars/
│   │   └── Cards/
│   ├── contexts/
│   │   └── AuthContext.js
│   ├── layouts/
│   │   └── Admin.js
│   ├── services/
│   │   └── api.js
│   ├── views/
│   │   └── admin/
│   │       ├── Dashboard.js
│   │       ├── Users.js
│   │       ├── Subscriptions.js
│   │       └── EPapers.js
│   └── App.js
├── .env
├── package.json
└── STEPS.md
```

## API Integration

### Authentication Endpoints
- `POST /auth/login` - User login
- `POST /auth/logout` - User logout
- `GET /auth/me` - Get current user info

### User Management Endpoints
- `GET /users/` - Get all users
- `PUT /users/{user_id}` - Update user
- `DELETE /users/{user_id}` - Delete user

### Subscription Endpoints
- `GET /subscriptions/` - Get all subscriptions
- `GET /subscriptions/plans` - Get subscription plans
- `POST /subscriptions/` - Create subscription

### ePaper Endpoints
- `GET /epaper/` - Get all ePapers
- `POST /epaper/upload` - Upload new ePaper
- `DELETE /epaper/{epaper_id}` - Delete ePaper
- `GET /epaper/{epaper_id}/download` - Download ePaper
- `GET /epaper/{epaper_id}/preview` - Preview ePaper

## Usage Instructions

### 1. Login
1. Navigate to the dashboard URL
2. Enter admin credentials
3. Click "Sign In" to access the dashboard

### 2. Managing Users
1. Click "Users" in the sidebar
2. View all registered users in the table
3. Click "Edit" to modify user details
4. Use the role dropdown to change user permissions

### 3. Managing Subscriptions
1. Click "Subscriptions" in the sidebar
2. Switch between "User Subscriptions" and "Subscription Plans" tabs
3. View subscription status and details
4. Monitor subscription analytics

### 4. Managing ePapers
1. Click "ePapers" in the sidebar
2. Click "Upload ePaper" to add new content
3. Fill in title, description, and publication date
4. Select PDF file and upload
5. Use action buttons to preview, download, or delete ePapers

### 5. Viewing Analytics
1. The Dashboard page shows comprehensive analytics
2. View key metrics in the stats cards
3. Analyze monthly growth trends in the line chart
4. Review subscription distribution in the doughnut chart
5. Monitor recent user activity in the table

## Customization

### Branding
- Update `REACT_APP_APP_NAME` in `.env` for app name
- Modify sidebar branding in `src/components/Sidebar/Sidebar.js`
- Update colors and styling in Tailwind CSS classes

### Adding New Features
1. Create new components in `src/views/admin/`
2. Add routes in `src/layouts/Admin.js`
3. Update sidebar navigation in `src/components/Sidebar/Sidebar.js`
4. Add API endpoints in `src/services/api.js`

## Troubleshooting

### Common Issues

1. **Login Issues**
   - Verify FastAPI backend is running
   - Check API URL in `.env` file
   - Ensure admin user exists in the backend

2. **Chart Display Issues**
   - Verify chart.js and react-chartjs-2 are installed
   - Check console for JavaScript errors
   - Ensure data is being fetched correctly

3. **File Upload Issues**
   - Check file size limits
   - Verify PDF file format
   - Ensure backend upload endpoint is working

4. **Styling Issues**
   - Verify Tailwind CSS is properly configured
   - Check for conflicting CSS classes
   - Ensure all dependencies are installed

### Development Tips
- Use browser developer tools for debugging
- Check network tab for API call issues
- Monitor console for JavaScript errors
- Use React Developer Tools for component debugging

## Production Deployment

### Build for Production
```bash
npm run build
```

### Environment Variables
Update `.env` for production:
```env
REACT_APP_API_URL=https://your-api-domain.com
REACT_APP_APP_NAME=Pravasi ePaper Admin
```

### Deployment Options
- Static hosting (Netlify, Vercel, GitHub Pages)
- Web servers (Apache, Nginx)
- Cloud platforms (AWS S3, Google Cloud Storage)

## Security Considerations
- All routes are protected with authentication
- JWT tokens are stored securely in cookies
- API calls include proper authorization headers
- File uploads are validated on both client and server
- User roles are enforced throughout the application

## Support
For technical support or questions:
1. Check the troubleshooting section above
2. Review the FastAPI backend documentation
3. Consult the React and Notus React documentation
4. Check browser console for error messages
