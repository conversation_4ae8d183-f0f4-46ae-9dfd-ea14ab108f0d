import axios from 'axios';
import Cookies from 'js-cookie';
import { toast } from 'react-toastify';

// API Configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = Cookies.get('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      Cookies.remove('access_token');
      Cookies.remove('user');
      window.location.href = '/auth/login';
    } else if (error.response?.status >= 500) {
      toast.error('Server error. Please try again later.');
    } else if (error.response?.data?.detail) {
      toast.error(error.response.data.detail);
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  refresh: () => api.post('/auth/refresh'),
  profile: () => api.get('/auth/profile'),
};

// Users API
export const usersAPI = {
  getProfile: () => api.get('/users/profile'),
  updateProfile: (data) => api.put('/users/profile', data),
  changePassword: (data) => api.post('/users/change-password', data),
  getAllUsers: () => api.get('/users/'),
  getUserById: (id) => api.get(`/users/${id}`),
  updateUser: (id, data) => api.put(`/users/${id}`, data),
  deleteUser: (id) => api.delete(`/users/${id}`),
};

// Subscriptions API
export const subscriptionsAPI = {
  getPlans: () => api.get('/subscriptions/plans'),
  getStatus: () => api.get('/subscriptions/status'),
  subscribe: (planId) => api.post('/subscriptions/subscribe', { plan_id: planId }),
  cancel: () => api.post('/subscriptions/cancel'),
  getAllSubscriptions: () => api.get('/subscriptions/'),
  createPlan: (data) => api.post('/subscriptions/plans', data),
  updatePlan: (id, data) => api.put(`/subscriptions/plans/${id}`, data),
};

// ePaper API
export const epaperAPI = {
  getEpapers: (params) => api.get('/epaper/', { params }),
  getEpaperById: (id) => api.get(`/epaper/${id}`),
  downloadEpaper: (id) => api.get(`/epaper/${id}/download`, { responseType: 'blob' }),
  getCategories: () => api.get('/epaper/categories/list'),
  uploadEpaper: (formData) => api.post('/epaper/', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  updateEpaper: (id, data) => api.put(`/epaper/${id}`, data),
  deleteEpaper: (id) => api.delete(`/epaper/${id}`),
  getAllEpapers: () => api.get('/epaper/admin/all'),
};

// Health check
export const healthAPI = {
  check: () => api.get('/health'),
};

export default api;
