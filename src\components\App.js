import React from "react";
import { Switch, Route, Redirect } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";

// layouts
import Admin from "../layouts/Admin.js";
import Auth from "../layouts/Auth.js";

export default function App() {
  const { isAuthenticated, loading } = useAuth();

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-blueGray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-blueGray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <Switch>
      {/* Authentication routes - accessible when NOT authenticated */}
      <Route path="/auth">
        {isAuthenticated ? <Redirect to="/admin/dashboard" /> : <Auth />}
      </Route>
      
      {/* Admin routes - accessible when authenticated */}
      <Route path="/admin">
        {isAuthenticated ? <Admin /> : <Redirect to="/auth/login" />}
      </Route>
      
      {/* Root redirect based on authentication status */}
      <Route path="/" exact>
        {isAuthenticated ? <Redirect to="/admin/dashboard" /> : <Redirect to="/auth/login" />}
      </Route>
      
      {/* Catch all redirect */}
      <Redirect from="*" to={isAuthenticated ? "/admin/dashboard" : "/auth/login"} />
    </Switch>
  );
}
