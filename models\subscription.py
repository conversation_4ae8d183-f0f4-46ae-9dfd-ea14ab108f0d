from sqlalchemy import Column, Inte<PERSON>, <PERSON>, <PERSON><PERSON>an, DateTime, ForeignKey, Enum
from sqlalchemy.types import Numeric
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database.database import Base
import enum
from datetime import datetime, timedelta

class PlanType(enum.Enum):
    TRIAL = "trial"
    MONTHLY = "monthly"
    YEARLY = "yearly"

class SubscriptionStatus(enum.Enum):
    ACTIVE = "active"
    EXPIRED = "expired"
    CANCELLED = "cancelled"
    PENDING = "pending"

class Plan(Base):
    __tablename__ = "plans"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(String, nullable=True)
    plan_type = Column(Enum(PlanType), nullable=False)
    price = Column(Numeric(10, 2), nullable=False)
    duration_days = Column(Integer, nullable=False)  # Duration in days
    is_active = Column(Boolean, default=True)
    features = Column(String, nullable=True)  # JSON string of features
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user_subscriptions = relationship("UserSubscription", back_populates="plan")

    @property
    def is_trial(self) -> bool:
        """Check if this is a trial plan."""
        return self.plan_type == PlanType.TRIAL

    @property
    def is_monthly(self) -> bool:
        """Check if this is a monthly plan."""
        return self.plan_type == PlanType.MONTHLY

    @property
    def is_yearly(self) -> bool:
        """Check if this is a yearly plan."""
        return self.plan_type == PlanType.YEARLY

class UserSubscription(Base):
    __tablename__ = "user_subscriptions"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    plan_id = Column(Integer, ForeignKey("plans.id"), nullable=False)
    status = Column(Enum(SubscriptionStatus), default=SubscriptionStatus.PENDING)
    start_date = Column(DateTime(timezone=True), nullable=False)
    end_date = Column(DateTime(timezone=True), nullable=False)
    payment_id = Column(String, nullable=True)  # External payment gateway ID
    auto_renew = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="subscriptions")
    plan = relationship("Plan", back_populates="user_subscriptions")

    @property
    def is_active(self) -> bool:
        """Check if subscription is currently active."""
        now = datetime.utcnow()
        return (
            self.status == SubscriptionStatus.ACTIVE and
            self.start_date <= now <= self.end_date
        )

    @property
    def is_expired(self) -> bool:
        """Check if subscription has expired."""
        now = datetime.utcnow()
        return now > self.end_date

    @property
    def days_remaining(self) -> int:
        """Get number of days remaining in subscription."""
        if self.is_expired:
            return 0
        now = datetime.utcnow()
        return (self.end_date - now).days

    def extend_subscription(self, days: int):
        """Extend subscription by specified number of days."""
        self.end_date = self.end_date + timedelta(days=days)

    def cancel_subscription(self):
        """Cancel the subscription."""
        self.status = SubscriptionStatus.CANCELLED
        self.auto_renew = False
