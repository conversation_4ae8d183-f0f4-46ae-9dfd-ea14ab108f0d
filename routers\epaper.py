from typing import List, Optional
from datetime import datetime, timedelta
import os
import secrets
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Query
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from pydantic import BaseModel

from database.database import get_db
from models.user import User
from models.epaper import EPaper
from models.subscription import UserSubscription, SubscriptionStatus
from dependencies.auth import get_current_active_user, get_admin_user

router = APIRouter(prefix="/epaper", tags=["epaper"])

# Pydantic models
class EPaperCreate(BaseModel):
    title: str
    description: Optional[str] = None
    publish_date: datetime
    category: Optional[str] = None
    tags: Optional[str] = None

class EPaperUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    publish_date: Optional[datetime] = None
    category: Optional[str] = None
    tags: Optional[str] = None
    is_active: Optional[bool] = None

class EPaperResponse(BaseModel):
    id: int
    title: str
    description: Optional[str]
    publish_date: datetime
    file_size: int
    page_count: Optional[int]
    is_active: bool
    created_at: datetime
    download_count: int

    class Config:
        from_attributes = True

class EPaperListResponse(BaseModel):
    id: int
    title: str
    description: Optional[str]
    publish_date: datetime
    file_size: int
    page_count: Optional[int]
    created_at: datetime

    class Config:
        from_attributes = True

# Helper functions
def check_user_subscription(user: User, db: Session) -> bool:
    """Check if user has active subscription."""
    if user.is_admin:
        return True
    
    active_subscription = db.query(UserSubscription).filter(
        UserSubscription.user_id == user.id,
        UserSubscription.status == SubscriptionStatus.ACTIVE,
        UserSubscription.end_date > datetime.utcnow()
    ).first()
    
    return active_subscription is not None

def generate_secure_filename(original_filename: str) -> str:
    """Generate a secure filename with timestamp and random string."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    random_string = secrets.token_hex(8)
    file_extension = os.path.splitext(original_filename)[1]
    return f"{timestamp}_{random_string}{file_extension}"

# Public endpoints (require subscription)
@router.get("/", response_model=List[EPaperListResponse])
async def list_epapers(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    category: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """List available ePapers (requires active subscription)."""
    if not check_user_subscription(current_user, db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Active subscription required to access ePapers"
        )
    
    query = db.query(EPaper).filter(EPaper.is_active == True)
    
    # Apply filters
    if category:
        query = query.filter(EPaper.category == category)

    if search:
        query = query.filter(
            (EPaper.title.contains(search)) |
            (EPaper.description.contains(search)) |
            (EPaper.tags.contains(search))
        )
    
    epapers = query.order_by(EPaper.publish_date.desc()).offset(skip).limit(limit).all()
    return epapers

@router.get("/{epaper_id}", response_model=EPaperResponse)
async def get_epaper(
    epaper_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get ePaper details (requires active subscription)."""
    if not check_user_subscription(current_user, db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Active subscription required to access ePapers"
        )
    
    epaper = db.query(EPaper).filter(EPaper.id == epaper_id, EPaper.is_active == True).first()
    if not epaper:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="ePaper not found"
        )
    
    return epaper

@router.get("/{epaper_id}/download")
async def download_epaper(
    epaper_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Download ePaper PDF (requires active subscription)."""
    if not check_user_subscription(current_user, db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Active subscription required to download ePapers"
        )
    
    epaper = db.query(EPaper).filter(EPaper.id == epaper_id, EPaper.is_active == True).first()
    if not epaper:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="ePaper not found"
        )
    
    # Check if file exists
    if not epaper.file_exists:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="ePaper file not found on server"
        )
    
    # Increment download count
    epaper.download_count += 1
    db.commit()
    
    # Return file
    return FileResponse(
        path=epaper.file_path,
        filename=f"{epaper.title}.pdf",
        media_type="application/pdf"
    )

@router.get("/categories/list")
async def get_categories(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get list of available categories."""
    if not check_user_subscription(current_user, db):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Active subscription required"
        )
    
    categories = db.query(EPaper.category).filter(
        EPaper.category.isnot(None),
        EPaper.is_active == True
    ).distinct().all()
    
    return [cat[0] for cat in categories if cat[0]]

# Admin endpoints
@router.post("/", response_model=EPaperResponse)
async def create_epaper(
    title: str,
    publish_date: str,
    description: Optional[str] = None,
    category: Optional[str] = None,
    tags: Optional[str] = None,
    file: UploadFile = File(...),
    admin_user: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """Upload new ePaper (admin only)."""
    # Validate file type
    if not file.content_type == "application/pdf":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Only PDF files are allowed"
        )

    # Generate secure filename
    secure_filename = generate_secure_filename(file.filename)
    file_path = os.path.join("media", "epapers", secure_filename)

    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(file_path), exist_ok=True)

    # Save file
    with open(file_path, "wb") as buffer:
        content = await file.read()
        buffer.write(content)

    # Parse publication date
    try:
        pub_date = datetime.fromisoformat(publish_date.replace('Z', '+00:00'))
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid publication date format. Use ISO format."
        )

    # Create ePaper record
    new_epaper = EPaper(
        title=title,
        description=description,
        publish_date=pub_date,
        category=category,
        tags=tags,
        file_path=file_path,
        file_size=len(content),
        uploaded_by=admin_user.id
    )

    db.add(new_epaper)
    db.commit()
    db.refresh(new_epaper)

    return new_epaper

@router.put("/{epaper_id}", response_model=EPaperResponse)
async def update_epaper(
    epaper_id: int,
    epaper_update: EPaperUpdate,
    admin_user: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """Update ePaper (admin only)."""
    epaper = db.query(EPaper).filter(EPaper.id == epaper_id).first()
    if not epaper:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="ePaper not found"
        )
    
    # Update fields
    if epaper_update.title is not None:
        epaper.title = epaper_update.title
    if epaper_update.description is not None:
        epaper.description = epaper_update.description
    if epaper_update.publish_date is not None:
        epaper.publish_date = epaper_update.publish_date
    if epaper_update.category is not None:
        epaper.category = epaper_update.category
    if epaper_update.tags is not None:
        epaper.tags = epaper_update.tags
    if epaper_update.is_active is not None:
        epaper.is_active = epaper_update.is_active
    
    db.commit()
    db.refresh(epaper)
    
    return epaper

@router.delete("/{epaper_id}")
async def delete_epaper(
    epaper_id: int,
    admin_user: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """Delete ePaper (admin only)."""
    epaper = db.query(EPaper).filter(EPaper.id == epaper_id).first()
    if not epaper:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="ePaper not found"
        )
    
    # Delete file from filesystem
    if os.path.exists(epaper.file_path):
        os.remove(epaper.file_path)
    
    # Delete from database
    db.delete(epaper)
    db.commit()
    
    return {"message": f"ePaper '{epaper.title}' deleted successfully"}

@router.get("/admin/all", response_model=List[EPaperResponse])
async def list_all_epapers(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    include_inactive: bool = Query(False),
    admin_user: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """List all ePapers including inactive ones (admin only)."""
    query = db.query(EPaper)
    
    if not include_inactive:
        query = query.filter(EPaper.is_active == True)
    
    epapers = query.order_by(EPaper.created_at.desc()).offset(skip).limit(limit).all()
    return epapers
