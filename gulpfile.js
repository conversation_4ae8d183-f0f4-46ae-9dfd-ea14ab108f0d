const gulp = require("gulp");
const gap = require("gulp-append-prepend");

gulp.task("licenses", async function () {
  // this is to add copyright notice in the production mode for the minified js
  gulp
    .src("build/static/js/*chunk.js", { base: "./" })
    .pipe(
      gap.prependText(`/*!

=========================================================
* Pravasi Prapancha ePaper Admin Dashboard
=========================================================

* A comprehensive admin dashboard for managing the Pravasi Prapancha ePaper platform
* Built with React and modern web technologies
* Copyright 2024 Pravasi Prapancha ePaper

=========================================================

*/`)
    )
    .pipe(gulp.dest("./", { overwrite: true }));

  // this is to add license header in the production mode for the minified html
  gulp
    .src("build/index.html", { base: "./" })
    .pipe(
      gap.prependText(`<!--

=========================================================
* Pravasi Prapancha ePaper Admin Dashboard
=========================================================

* A comprehensive admin dashboard for managing the Pravasi Prapancha ePaper platform
* Built with React and modern web technologies
* Copyright 2024 Pravasi Prapancha ePaper

=========================================================

-->`)
    )
    .pipe(gulp.dest("./", { overwrite: true }));

  // this is to add license header in the production mode for the minified css
  gulp
    .src("build/static/css/*chunk.css", { base: "./" })
    .pipe(
      gap.prependText(`/*!

=========================================================
* Pravasi Prapancha ePaper Admin Dashboard
=========================================================

* A comprehensive admin dashboard for managing the Pravasi Prapancha ePaper platform
* Built with React and modern web technologies
* Copyright 2024 Pravasi Prapancha ePaper

=========================================================

*/`)
    )
    .pipe(gulp.dest("./", { overwrite: true }));
  return;
});
