import React, { useState, useEffect } from "react";
import { usersAPI, subscriptionsAPI, epaperAPI } from "../../services/api";

// components

import CardStats from "components/Cards/CardStats.js";

export default function HeaderStats() {
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalSubscriptions: 0,
    totalEpapers: 0,
    activeSubscriptions: 0
  });

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      const [usersRes, subscriptionsRes, epapersRes] = await Promise.all([
        usersAPI.getAllUsers(),
        subscriptionsAPI.getAllSubscriptions(),
        epaperAPI.getAllEpapers()
      ]);

      const users = usersRes.data;
      const subscriptions = subscriptionsRes.data;
      const epapers = epapersRes.data;

      const activeSubscriptions = subscriptions.filter(sub => sub.status === 'active').length;

      setStats({
        totalUsers: users.length,
        totalSubscriptions: subscriptions.length,
        totalEpapers: epapers.length,
        activeSubscriptions
      });
    } catch (error) {
      console.error("Error fetching stats:", error);
    }
  };
  return (
    <>
      {/* Header */}
      <div className="relative bg-lightBlue-600 md:pt-32 pb-32 pt-12">
        <div className="px-4 md:px-10 mx-auto w-full">
          <div>
            {/* Card stats */}
            <div className="flex flex-wrap">
              <div className="w-full lg:w-6/12 xl:w-3/12 px-4">
                <CardStats
                  statSubtitle="TOTAL USERS"
                  statTitle={stats.totalUsers.toString()}
                  statArrow="up"
                  statPercent="0"
                  statPercentColor="text-emerald-500"
                  statDescripiron="Registered users"
                  statIconName="fas fa-users"
                  statIconColor="bg-red-500"
                />
              </div>
              <div className="w-full lg:w-6/12 xl:w-3/12 px-4">
                <CardStats
                  statSubtitle="ACTIVE SUBSCRIPTIONS"
                  statTitle={stats.activeSubscriptions.toString()}
                  statArrow="up"
                  statPercent="0"
                  statPercentColor="text-emerald-500"
                  statDescripiron="Active subscriptions"
                  statIconName="fas fa-chart-pie"
                  statIconColor="bg-orange-500"
                />
              </div>
              <div className="w-full lg:w-6/12 xl:w-3/12 px-4">
                <CardStats
                  statSubtitle="TOTAL SUBSCRIPTIONS"
                  statTitle={stats.totalSubscriptions.toString()}
                  statArrow="up"
                  statPercent="0"
                  statPercentColor="text-emerald-500"
                  statDescripiron="All subscriptions"
                  statIconName="fas fa-credit-card"
                  statIconColor="bg-pink-500"
                />
              </div>
              <div className="w-full lg:w-6/12 xl:w-3/12 px-4">
                <CardStats
                  statSubtitle="TOTAL EPAPERS"
                  statTitle={stats.totalEpapers.toString()}
                  statArrow="up"
                  statPercent="0"
                  statPercentColor="text-emerald-500"
                  statDescripiron="Available ePapers"
                  statIconName="fas fa-newspaper"
                  statIconColor="bg-lightBlue-500"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
