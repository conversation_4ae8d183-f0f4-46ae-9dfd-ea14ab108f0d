from sqlalchemy import <PERSON>umn, Inte<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DateTime, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database.database import Base
from passlib.context import CryptContext
import enum

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class UserRole(enum.Enum):
    ADMIN = "admin"
    SUBSCRIBER = "subscriber"

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    username = Column(String, unique=True, index=True, nullable=False)
    full_name = Column(String, nullable=True)
    hashed_password = Column(String, nullable=False)
    is_active = Column(Boolean, default=True)
    role = Column(Enum(UserRole), default=UserRole.SUBSCRIBER)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    subscriptions = relationship("UserSubscription", back_populates="user")

    def verify_password(self, password: str) -> bool:
        """Verify a password against the hash."""
        return pwd_context.verify(password, self.hashed_password)

    @staticmethod
    def get_password_hash(password: str) -> str:
        """Generate password hash."""
        return pwd_context.hash(password)

    def set_password(self, password: str):
        """Set user password."""
        self.hashed_password = self.get_password_hash(password)

    @property
    def is_admin(self) -> bool:
        """Check if user is admin."""
        return self.role == UserRole.ADMIN

    @property
    def is_subscriber(self) -> bool:
        """Check if user is subscriber."""
        return self.role == UserRole.SUBSCRIBER
