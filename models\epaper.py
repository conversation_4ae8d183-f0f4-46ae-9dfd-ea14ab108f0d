from sqlalchemy import Column, Inte<PERSON>, String, Boolean, DateTime, Text, BigInteger
from sqlalchemy.sql import func
from database.database import Base
import os
from datetime import datetime

class EPaper(Base):
    __tablename__ = "epapers"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String, nullable=False, index=True)
    description = Column(Text, nullable=True)
    publish_date = Column(DateTime(timezone=True), nullable=False)
    category = Column(String, nullable=True, index=True)  # Category for filtering
    tags = Column(String, nullable=True)  # Comma-separated tags
    file_path = Column(String, nullable=False)  # Path to original PDF
    file_size = Column(BigInteger, nullable=False)  # File size in bytes
    preview_path = Column(String, nullable=True)  # Path to preview image
    thumbnail_path = Column(String, nullable=True)  # Path to thumbnail
    page_count = Column(Integer, nullable=True)  # Number of pages in PDF
    uploaded_by = Column(Integer, nullable=True)  # User ID who uploaded
    is_active = Column(Boolean, default=True)
    is_featured = Column(Boolean, default=False)
    download_count = Column(Integer, default=0)
    view_count = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    @property
    def file_exists(self) -> bool:
        """Check if the PDF file exists on disk."""
        return os.path.exists(self.file_path)

    @property
    def preview_exists(self) -> bool:
        """Check if preview image exists."""
        return self.preview_path and os.path.exists(self.preview_path)

    @property
    def thumbnail_exists(self) -> bool:
        """Check if thumbnail exists."""
        return self.thumbnail_path and os.path.exists(self.thumbnail_path)

    @property
    def file_size_mb(self) -> float:
        """Get file size in MB."""
        return round(self.file_size / (1024 * 1024), 2)

    @property
    def formatted_publish_date(self) -> str:
        """Get formatted publish date."""
        return self.publish_date.strftime("%B %d, %Y")

    @property
    def is_recent(self) -> bool:
        """Check if paper was published in last 7 days."""
        now = datetime.utcnow()
        days_diff = (now - self.publish_date).days
        return days_diff <= 7

    def increment_view_count(self):
        """Increment view count."""
        self.view_count += 1

    def increment_download_count(self):
        """Increment download count."""
        self.download_count += 1

    def get_file_extension(self) -> str:
        """Get file extension."""
        return os.path.splitext(self.file_path)[1].lower()

    def is_pdf(self) -> bool:
        """Check if file is PDF."""
        return self.get_file_extension() == '.pdf'
