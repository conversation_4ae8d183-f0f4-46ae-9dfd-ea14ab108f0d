#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create sample data for Pravasi ePaper platform.
Run this after the database is created to populate with initial data.
"""

from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from database.database import SessionLocal, engine
from models import Base
from models.user import User, UserRole
from models.subscription import Plan, UserSubscription, PlanType, SubscriptionStatus

def create_sample_data():
    """Create sample data for testing."""
    # Create database tables
    Base.metadata.create_all(bind=engine)
    
    db = SessionLocal()
    
    try:
        # Create admin user
        admin_user = User(
            email="<EMAIL>",
            username="admin",
            full_name="System Administrator",
            role=UserRole.ADMIN,
            is_active=True
        )
        admin_user.set_password("admin123")
        db.add(admin_user)
        
        # Create sample regular user
        regular_user = User(
            email="<EMAIL>",
            username="testuser",
            full_name="Test User",
            role=UserRole.SUBSCRIBER,
            is_active=True
        )
        regular_user.set_password("user123")
        db.add(regular_user)
        
        # Create subscription plans
        trial_plan = Plan(
            name="Free Trial",
            description="7-day free trial with full access",
            plan_type=PlanType.TRIAL,
            price=0.00,
            duration_days=7,
            features='{"max_downloads": 10, "access_archive": false}'
        )
        db.add(trial_plan)
        
        monthly_plan = Plan(
            name="Monthly Subscription",
            description="Full access for 30 days",
            plan_type=PlanType.MONTHLY,
            price=9.99,
            duration_days=30,
            features='{"max_downloads": -1, "access_archive": true}'
        )
        db.add(monthly_plan)
        
        yearly_plan = Plan(
            name="Yearly Subscription",
            description="Full access for 365 days with 20% discount",
            plan_type=PlanType.YEARLY,
            price=95.99,
            duration_days=365,
            features='{"max_downloads": -1, "access_archive": true, "priority_support": true}'
        )
        db.add(yearly_plan)
        
        # Commit to get IDs
        db.commit()
        
        # Create active subscription for test user
        start_date = datetime.utcnow()
        end_date = start_date + timedelta(days=30)
        
        user_subscription = UserSubscription(
            user_id=regular_user.id,
            plan_id=monthly_plan.id,
            status=SubscriptionStatus.ACTIVE,
            start_date=start_date,
            end_date=end_date,
            payment_id="test_payment_123"
        )
        db.add(user_subscription)
        
        db.commit()
        
        print("✅ Sample data created successfully!")
        print("\n📋 Created Users:")
        print(f"   Admin: <EMAIL> / admin123")
        print(f"   User:  <EMAIL> / user123")
        print("\n📋 Created Plans:")
        print(f"   Trial: {trial_plan.name} - ${trial_plan.price}")
        print(f"   Monthly: {monthly_plan.name} - ${monthly_plan.price}")
        print(f"   Yearly: {yearly_plan.name} - ${yearly_plan.price}")
        print(f"\n📋 Test user has active monthly subscription until {end_date.strftime('%Y-%m-%d')}")
        
    except Exception as e:
        print(f"❌ Error creating sample data: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_sample_data()
